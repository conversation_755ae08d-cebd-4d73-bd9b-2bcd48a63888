#!/bin/bash

# Test script for assignment filtering functionality
BASE_URL="http://localhost:8093/move"

echo "Testing Assignment Filtering Functionality"
echo "=========================================="

# Test 1: Trip listing with single assignment type
echo "Test 1: Trip listing with assignment type [EXTERNAL] only"
curl -X POST "${BASE_URL}/v1/trips/list" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: CFR" \
  -d '{
    "pagination": {"page": 0, "size": 5},
    "sort": {"field": "createdAt", "order": "DESC"},
    "filter": {
      "assignment": {
        "type": ["EXTERNAL"]
      }
    }
  }' | jq '.totalRecords, .data[0].assignmentDetails.type' 2>/dev/null || echo "Failed"

echo -e "\n"

# Test 2: Trip listing with assignment identifiers only
echo "Test 2: Trip listing with assignment identifiers ['1', '2'] only"
curl -X POST "${BASE_URL}/v1/trips/list" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: CFR" \
  -d '{
    "pagination": {"page": 0, "size": 5},
    "sort": {"field": "createdAt", "order": "DESC"},
    "filter": {
      "assignment": {
        "identifiers": ["1", "2"]
      }
    }
  }' | jq '.totalRecords, .data[0].assignmentDetails.identifiers.code' 2>/dev/null || echo "Failed"

echo -e "\n"

# Test 3: Trip listing with both assignment type and identifiers
echo "Test 3: Trip listing with assignment type [EXTERNAL] and identifiers ['1']"
curl -X POST "${BASE_URL}/v1/trips/list" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: CFR" \
  -d '{
    "pagination": {"page": 0, "size": 5},
    "sort": {"field": "createdAt", "order": "DESC"},
    "filter": {
      "assignment": {
        "type": ["EXTERNAL"],
        "identifiers": ["1"]
      }
    }
  }' | jq '.totalRecords, .data[0].assignmentDetails' 2>/dev/null || echo "Failed"

echo -e "\n"

# Test 4: Trip listing with invalid identifiers (should return 0 results)
echo "Test 4: Trip listing with invalid identifiers (empty strings)"
curl -X POST "${BASE_URL}/v1/trips/list" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: CFR" \
  -d '{
    "pagination": {"page": 0, "size": 5},
    "sort": {"field": "createdAt", "order": "DESC"},
    "filter": {
      "assignment": {
        "identifiers": ["", "   ", null]
      }
    }
  }' | jq '.totalRecords' 2>/dev/null || echo "Failed"

echo -e "\n"

# Test 5: Trip listing with multiple assignment types
echo "Test 5: Trip listing with multiple assignment types [EXTERNAL, INTERNAL]"
curl -X POST "${BASE_URL}/v1/trips/list" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: CFR" \
  -d '{
    "pagination": {"page": 0, "size": 5},
    "sort": {"field": "createdAt", "order": "DESC"},
    "filter": {
      "assignment": {
        "type": ["EXTERNAL", "INTERNAL"]
      }
    }
  }' | jq '.totalRecords' 2>/dev/null || echo "Failed"

echo -e "\n"

# Test 6: Shipment listing with assignment filtering
echo "Test 5: Shipment listing with assignment type [EXTERNAL]"
curl -X POST "${BASE_URL}/v1/shipments/list" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: CFR" \
  -d '{
    "pagination": {"page": 0, "size": 5},
    "sort": {"field": "createdAt", "order": "DESC"},
    "filter": {
      "assignment": {
        "type": ["EXTERNAL"]
      }
    }
  }' | jq '.totalRecords' 2>/dev/null || echo "Failed"

echo -e "\n"

# Test 7: Transport Order listing with assignment filtering
echo "Test 7: Transport Order listing with assignment identifiers ['1', '2']"
curl -X POST "${BASE_URL}/v1/transport-orders/list" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: CFR" \
  -d '{
    "pagination": {"page": 0, "size": 5},
    "sort": {"field": "createdAt", "order": "DESC"},
    "filter": {
      "assignment": {
        "identifiers": ["1", "2"]
      }
    }
  }' | jq '.totalRecords' 2>/dev/null || echo "Failed"

echo -e "\n"
echo "Assignment filtering tests completed!"
