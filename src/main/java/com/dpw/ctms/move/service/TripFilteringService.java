package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.ShipmentFieldConstants;
import com.dpw.ctms.move.constants.TransportOrderFieldConstants;
import com.dpw.ctms.move.constants.TripFieldConstants;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.mapper.TripMappingService;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.Exception;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.TripFieldConstants.DELETED_AT;

/**
 * Service class for filtering, sorting, and paginating Trip entities
 */
// TODO: Coverage needs to be improved for this class
@Slf4j
@Service
@RequiredArgsConstructor
public class TripFilteringService {

    private final TripRepository tripRepository;
    private final TripMappingService tripMappingService;

    // Default pagination values
    private static final int DEFAULT_PAGE_NO = 0;
    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_PAGE_SIZE = 500;

    // Default sorting values
    private static final String DEFAULT_SORT_BY = TripFieldConstants.DEFAULT_SORT_BY;
    private static final String DEFAULT_SORT_ORDER = TripFieldConstants.DEFAULT_SORT_ORDER;

    /**
     * Main method to filter trips based on the provided request
     *
     * @param request The filtering request containing pagination, sorting, and
     *                filter criteria
     * @return List of filtered and mapped trip responses
     */
    @Transactional(readOnly = true)
    public ListResponse<TripListingResponse> filterTrips(TripListingRequest request) {
        try {
            log.info("Starting trip filtering with request: {}", request);

            // Build specifications based on filter criteria
            Specification<Trip> specification = buildSpecification(request.getFilter());

            // Create pageable object with sorting
            Pageable pageable = createPageable(request.getPagination(), request.getSort());

            // Execute query with specifications and pagination
            Page<Trip> tripPage = tripRepository.findAll(specification, pageable);

            Long totalElements = tripPage.getTotalElements();
            log.info("Found {} trips matching criteria", totalElements);

            // Map entities to response DTOs
            List<TripListingResponse> tripResponses = tripPage.getContent().stream()
                    .map(tripMappingService::mapToResponse)
                    .collect(Collectors.toList());

            // Build and return the enhanced response with pagination metadata
            return ListResponse.<TripListingResponse>builder()
                    .data(tripResponses)
                    .totalRecords(totalElements)
                    .build();

        } catch (Exception e) {
            log.error("Error occurred while filtering trips", e);
            throw new RuntimeException("Failed to filter trips", e);
        }
    }

    /**
     * Builds JPA Specification based on filter criteria
     *
     * @param filter The filter criteria
     * @return Combined specification for all filter conditions
     */
    private Specification<Trip> buildSpecification(TripListingRequest.Filter filter) {
        Specification<Trip> spec = Specification
                .where((root, query, builder) -> builder.isNull(root.get(DELETED_AT)));

        if (filter == null) {
            return spec;
        }

        // Filter by trip IDs
        if (!CollectionUtils.isEmpty(filter.getTripIds())) {
            spec = spec.and(tripIdsIn(filter.getTripIds()));
        }

        // Filter by trip statuses
        if (!CollectionUtils.isEmpty(filter.getTripStatuses())) {
            spec = spec.and(statusesIn(filter.getTripStatuses()));
        }

        // Filter by shipment IDs
        if (!CollectionUtils.isEmpty(filter.getShipmentIds())) {
            spec = spec.and(shipmentIdsIn(filter.getShipmentIds()));
        }

        // Filter by consignment IDs
        if (!CollectionUtils.isEmpty(filter.getConsignmentIds())) {
            spec = spec.and(consignmentIdsIn(filter.getConsignmentIds()));
        }

        // Filter by transport order IDs
        if (!CollectionUtils.isEmpty(filter.getTransportOrderIds())) {
            spec = spec.and(transportOrderIdsIn(filter.getTransportOrderIds()));
        }

        // Filter by transport order statuses
        if (!CollectionUtils.isEmpty(filter.getTransportOrderStatuses())) {
            spec = spec.and(transportOrderStatusesIn(filter.getTransportOrderStatuses()));
        }

        // Filter by shipment statuses
        if (!CollectionUtils.isEmpty(filter.getShipmentStatuses())) {
            spec = spec.and(shipmentStatusesIn(filter.getShipmentStatuses()));
        }

        // Filter by customer order IDs
        if (!CollectionUtils.isEmpty(filter.getCustomerOrderIds())) {
            spec = spec.and(customerOrderIdsIn(filter.getCustomerOrderIds()));
        }

        // Filter by assignment
        if (filter.getAssignment() != null) {
            spec = spec.and(assignmentFilter(filter.getAssignment()));
        }

        // Filter by vehicle types
        if (!CollectionUtils.isEmpty(filter.getVehicleTypes())) {
            spec = spec.and(vehicleTypesIn(filter.getVehicleTypes()));
        }

        // Filter by vehicle IDs
        if (!CollectionUtils.isEmpty(filter.getVehicleIds())) {
            spec = spec.and(vehicleIdsIn(filter.getVehicleIds()));
        }

        // Filter by trailer IDs
        if (!CollectionUtils.isEmpty(filter.getTrailerIds())) {
            spec = spec.and(trailerIdsIn(filter.getTrailerIds()));
        }

        // Filter by vehicle operator IDs
        if (!CollectionUtils.isEmpty(filter.getVehicleOperatorIds())) {
            spec = spec.and(vehicleOperatorIdsIn(filter.getVehicleOperatorIds()));
        }

        // Filter by origin location ID
        if (StringUtils.hasText(filter.getOriginLocationId())) {
            spec = spec.and(originLocationIdEquals(filter.getOriginLocationId()));
        }

        // Filter by destination location ID
        if (StringUtils.hasText(filter.getDestinationLocationId())) {
            spec = spec.and(destinationLocationIdEquals(filter.getDestinationLocationId()));
        }

        // Filter by expected pickup date range
        if (filter.getExpectedStartDateRange() != null) {
            spec = spec.and(dateInRange(TripFieldConstants.EXPECTED_START_AT, filter.getExpectedStartDateRange()));
        }

        // Filter by expected delivery date range
        if (filter.getExpectedEndDateRange() != null) {
            spec = spec.and(dateInRange(TripFieldConstants.EXPECTED_END_AT, filter.getExpectedEndDateRange()));
        }

        // Filter by actual pickup date range
        if (filter.getActualStartDateRange() != null) {
            spec = spec.and(dateInRange(TripFieldConstants.ACTUAL_START_AT, filter.getActualStartDateRange()));
        }

        // Filter by actual delivery date range
        if (filter.getActualEndDateRange() != null) {
            spec = spec.and(dateInRange(TripFieldConstants.ACTUAL_END_AT, filter.getActualEndDateRange()));
        }

        if (filter.getIsPodAttached() != null) {
            spec = spec.and(isPodAttached(filter.getIsPodAttached()));
        }

        return spec;
    }

    /**
     * Creates Pageable object with proper validation and defaults
     */
    private Pageable createPageable(Pagination pagination,
                                    Sort sortRequest) {
        // Handle pagination
        int pageNo = DEFAULT_PAGE_NO;
        int pageSize = DEFAULT_PAGE_SIZE;

        if (pagination != null) {
            pageNo = Math.max(0, pagination.getPageNo());
            pageSize = pagination.getPageSize() > 0 ?
                    Math.min(pagination.getPageSize(), MAX_PAGE_SIZE) : DEFAULT_PAGE_SIZE;
        }

        // Handle sorting
        org.springframework.data.domain.Sort sort = createSort(sortRequest);

        return PageRequest.of(pageNo, pageSize, sort);
    }

    /**
     * Creates Sort object with validation and defaults
     */
    private org.springframework.data.domain.Sort createSort(Sort sortRequest) {
        String sortBy = DEFAULT_SORT_BY;
        Direction direction = Direction.fromString(DEFAULT_SORT_ORDER);

        if (sortRequest != null) {
            if (StringUtils.hasText(sortRequest.getSortBy())) {
                sortBy = validateAndMapSortField(sortRequest.getSortBy());
            }

            if (StringUtils.hasText(sortRequest.getSortOrder())) {
                try {
                    direction = Direction.fromString(sortRequest.getSortOrder());
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid sort order: {}, using default DESC", sortRequest.getSortOrder());
                }
            }
        }

        return org.springframework.data.domain.Sort.by(direction, sortBy);
    }

    /**
     * Validates and maps sort field names to actual entity fields
     * Supports comprehensive sorting options for Trip entities
     */
    private String validateAndMapSortField(String sortBy) {
        Map<String, String> fieldMapping = Map.ofEntries(
                // Basic Trip fields
                Map.entry(TripFieldConstants.SortFields.API_CODE, TripFieldConstants.CODE),
                Map.entry(TripFieldConstants.SortFields.API_STATUS, TripFieldConstants.STATUS),
                Map.entry(TripFieldConstants.SortFields.API_CREATED_AT, TripFieldConstants.CREATED_AT),
                Map.entry(TripFieldConstants.SortFields.API_UPDATED_AT, TripFieldConstants.UPDATED_AT),
                Map.entry(TripFieldConstants.SortFields.API_CREATED_BY, TripFieldConstants.CREATED_BY),
                Map.entry(TripFieldConstants.SortFields.API_UPDATED_BY, TripFieldConstants.UPDATED_BY),

                // Location fields
                Map.entry(TripFieldConstants.SortFields.API_ORIGIN_LOCATION, TripFieldConstants.EXTERNAL_ORIGIN_LOCATION_CODE),
                Map.entry(TripFieldConstants.SortFields.API_DESTINATION_LOCATION, TripFieldConstants.EXTERNAL_DESTINATION_LOCATION_CODE),

                // Timestamp fields (Trip entity)
                Map.entry(TripFieldConstants.SortFields.API_EXPECTED_START_AT, TripFieldConstants.EXPECTED_START_AT),
                Map.entry(TripFieldConstants.SortFields.API_EXPECTED_END_AT, TripFieldConstants.EXPECTED_END_AT),
                Map.entry(TripFieldConstants.SortFields.API_ACTUAL_START_AT, TripFieldConstants.ACTUAL_START_AT),
                Map.entry(TripFieldConstants.SortFields.API_ACTUAL_END_AT, TripFieldConstants.ACTUAL_END_AT),

                // Transport Order related fields
                Map.entry(TripFieldConstants.SortFields.API_TRANSPORT_ORDER_CODE, TripFieldConstants.TRANSPORT_ORDER_CODE),
                Map.entry(TripFieldConstants.SortFields.API_TRANSPORT_ORDER_STATUS, TripFieldConstants.TRANSPORT_ORDER_STATUS),
                Map.entry(TripFieldConstants.SortFields.API_ASSIGNMENT_TYPE, TripFieldConstants.TRANSPORT_ORDER_ASSIGNMENT_TYPE),
                Map.entry(TripFieldConstants.SortFields.API_VENDOR_ID, TripFieldConstants.TRANSPORT_ORDER_ASSIGNEE_IDENTIFIER),

                // Vehicle related fields (requires join)
                Map.entry(TripFieldConstants.SortFields.API_VEHICLE_REGISTRATION, TripFieldConstants.VEHICLE_REGISTRATION_NUMBER),
                Map.entry(TripFieldConstants.SortFields.API_VEHICLE_TYPE, TripFieldConstants.VEHICLE_EXTERNAL_TYPE_ID),
                Map.entry(TripFieldConstants.SortFields.API_VEHICLE_ID, TripFieldConstants.VEHICLE_EXTERNAL_RESOURCE_ID)
        );

        String mappedField = fieldMapping.get(sortBy);
        if (mappedField != null) {
            log.info("Mapped sort field '{}' to entity field '{}'", sortBy, mappedField);
            return mappedField;
        } else {
            log.warn("Invalid sort field '{}', using default '{}'", sortBy, DEFAULT_SORT_BY);
            return DEFAULT_SORT_BY;
        }
    }

    // Specification methods for different filter criteria
    private Specification<Trip> tripIdsIn(List<String> tripIds) {
        return (root, query, builder) -> root.get(TripFieldConstants.CODE).in(tripIds);
    }

    private Specification<Trip> statusesIn(List<String> statuses) {
        return (root, query, builder) -> {
            List<TripStatus> tripStatuses = statuses.stream()
                    .map(status -> {
                        try {
                            return TripStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.warn("Invalid trip status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return tripStatuses.isEmpty() ?
                    builder.disjunction() : root.get(TripFieldConstants.STATUS).in(tripStatuses);
        };
    }

    private Specification<Trip> transportOrderIdsIn(List<String> transportOrderIds) {
        return (root, query, builder) ->
                root.get(TripFieldConstants.TRANSPORT_ORDER).get(TripFieldConstants.CODE).in(transportOrderIds);
    }

    private Specification<Trip> customerOrderIdsIn(List<String> customerOrderIds) {
        return (root, query, builder) -> {
            Join<Trip, Shipment> shipmentJoin = root.join(TripFieldConstants.SHIPMENTS, JoinType.INNER);
            return builder.and(
                    shipmentJoin.get(TripFieldConstants.EXTERNAL_CUSTOMER_ORDER_ID).in(customerOrderIds),
                    builder.isNull(shipmentJoin.get(DELETED_AT)));
        };
    }

    private Specification<Trip> assignmentFilter(TripListingRequest.Assignment assignment) {
        return (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by assignment types if provided
            if (!CollectionUtils.isEmpty(assignment.getType())) {
                predicates.add(
                        root.get(TripFieldConstants.TRANSPORT_ORDER)
                                .get(TransportOrderFieldConstants.ASSIGNMENT_TYPE)
                                .in(assignment.getType())
                );
            }

            // Filter by identifiers if provided
            if (!CollectionUtils.isEmpty(assignment.getIdentifiers())) {
                // Validate identifiers
                List<String> validIdentifiers = assignment.getIdentifiers().stream()
                        .filter(id -> {
                            if (!StringUtils.hasText(id)) {
                                log.info("Invalid assignment identifier: {}", id);
                                return false;
                            }
                            return true;
                        })
                        .collect(Collectors.toList());

                if (validIdentifiers.isEmpty()) {
                    log.info("No valid assignment identifiers found");
                    return builder.disjunction();
                }

                predicates.add(
                        root.get(TripFieldConstants.TRANSPORT_ORDER)
                                .get(TripFieldConstants.ASSIGNEE_IDENTIFIER)
                                .in(validIdentifiers)
                );
            }

            return predicates.isEmpty() ? builder.conjunction() : builder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private Specification<Trip> vehicleTypesIn(List<String> vehicleTypes) {
        return (root, query, builder) -> {
            Join<Trip, VehicleResource> vehicleJoin = root.join(TripFieldConstants.VEHICLE_RESOURCE, JoinType.LEFT);
            return builder.and(
                    vehicleJoin.get(TripFieldConstants.EXTERNAL_VEHICLE_TYPE_ID).in(vehicleTypes),
                    builder.isNull(vehicleJoin.get(DELETED_AT)));
        };
    }

    private Specification<Trip> vehicleIdsIn(List<String> vehicleIds) {
        return (root, query, builder) -> {
            Join<Trip, VehicleResource> vehicleJoin = root.join(TripFieldConstants.VEHICLE_RESOURCE, JoinType.LEFT);
            return builder.and(
                    vehicleJoin.get(TripFieldConstants.EXTERNAL_RESOURCE_ID).in(vehicleIds),
                    builder.isNull(vehicleJoin.get(DELETED_AT)));
        };
    }

    private Specification<Trip> trailerIdsIn(List<String> trailerIds) {
        return (root, query, builder) -> {
            Join<Trip, TrailerResource> trailerJoin = root.join(TripFieldConstants.TRAILER_RESOURCES, JoinType.INNER);
            return builder.and(
                    trailerJoin.get(TripFieldConstants.EXTERNAL_RESOURCE_ID).in(trailerIds),
                    builder.isNull(trailerJoin.get(DELETED_AT)));
        };
    }

    private Specification<Trip> vehicleOperatorIdsIn(List<String> vehicleOperatorIds) {
        return (root, query, builder) -> {
            Join<Trip, VehicleOperatorResource> operatorJoin = root.join(TripFieldConstants.VEHICLE_OPERATOR_RESOURCES,
                    JoinType.INNER);
            return builder.and(
                    operatorJoin.get(TripFieldConstants.EXTERNAL_RESOURCE_ID).in(vehicleOperatorIds),
                    builder.isNull(operatorJoin.get(DELETED_AT)));
        };
    }

    private Specification<Trip> originLocationIdEquals(String originLocationId) {
        return (root, query, builder) ->
                builder.equal(root.get(TripFieldConstants.EXTERNAL_ORIGIN_LOCATION_CODE), originLocationId);
    }

    private Specification<Trip> destinationLocationIdEquals(String destinationLocationId) {
        return (root, query, builder) ->
                builder.equal(root.get(TripFieldConstants.EXTERNAL_DESTINATION_LOCATION_CODE), destinationLocationId);
    }

    private Specification<Trip> shipmentIdsIn(List<String> shipmentCodes) {
        return (root, query, builder) -> {
            Join<Trip, Shipment> shipmentJoin = root.join(TripFieldConstants.SHIPMENTS, JoinType.INNER);
            return builder.and(
                    shipmentJoin.get(TripFieldConstants.SHIPMENT_CODE).in(shipmentCodes),
                    builder.isNull(shipmentJoin.get(DELETED_AT)));
        };
    }

    private Specification<Trip> consignmentIdsIn(List<String> consignmentIds) {
        return (root, query, builder) -> {
            Join<Trip, Shipment> shipmentJoin = root.join(TripFieldConstants.SHIPMENTS, JoinType.INNER);
            return builder.and(
                    shipmentJoin.get(TripFieldConstants.EXTERNAL_CONSIGNMENT_ID).in(consignmentIds),
                    builder.isNull(shipmentJoin.get(DELETED_AT)));
        };
    }

    private Specification<Trip> transportOrderStatusesIn(List<String> transportOrderStatuses) {
        return (root, query, builder) -> {
            List<TransportOrderStatus> statuses = transportOrderStatuses.stream()
                    .map(status -> {
                        try {
                            return TransportOrderStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.warn("Invalid transport order status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return statuses.isEmpty() ?
                    builder.disjunction() : root.get(TripFieldConstants.TRANSPORT_ORDER).get(TripFieldConstants.STATUS).in(statuses);
        };
    }

    private Specification<Trip> shipmentStatusesIn(List<String> shipmentStatuses) {
        return (root, query, builder) -> {
            Join<Trip, Shipment> shipmentJoin = root.join(TripFieldConstants.SHIPMENTS, JoinType.INNER);
            List<ShipmentStatus> statuses = shipmentStatuses.stream()
                    .map(status -> {
                        try {
                            return ShipmentStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.warn("Invalid shipment status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return builder.and(
                    shipmentJoin.get(TripFieldConstants.STATUS).in(statuses),
                    builder.isNull(shipmentJoin.get(DELETED_AT)));
        };
    }

    private Specification<Trip> dateInRange(String fieldName, DateRange dateRange) {
        return (root, query, builder) -> {
            if (dateRange.getFrom() != null && dateRange.getTo() != null) {
                return builder.and(
                        builder.greaterThanOrEqualTo(root.get(fieldName).get("epoch"), dateRange.getFrom()),
                        builder.lessThanOrEqualTo(root.get(fieldName).get("epoch"), dateRange.getTo())
                );
            } else if (dateRange.getFrom() != null) {
                return builder.greaterThanOrEqualTo(root.get(fieldName).get("epoch"), dateRange.getFrom());
            } else if (dateRange.getTo() != null) {
                return builder.lessThanOrEqualTo(root.get(fieldName).get("epoch"), dateRange.getTo());
            }

            return builder.conjunction();
        };
    }

    private Specification<Trip> isPodAttached(boolean isPodAttached) {
        return (root, query, builder) -> {
            Join<Trip, Shipment> operatorJoin = root.join(TripFieldConstants.SHIPMENTS, JoinType.INNER);
            return builder.and(
                    operatorJoin.get(ShipmentFieldConstants.IS_DOCUMENT_ATTACHED).in(isPodAttached),
                    builder.isNull(operatorJoin.get(DELETED_AT)));
        };
    }
}
