package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportOrderListingResponse {
    private String code;
    private EnumLabelValueResponse status;
    private List<TransportOrderListingResponse.CustomerOrder> customerOrders;
    private List<TransportOrderListingResponse.Trip> trips;
    private TransportOrderListingResponse.AssignmentDetails assignmentDetails;
    private Long updatedAt;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CustomerOrder {
        private String id;
        private Long updatedAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Trip {
        private String code;
        private Long updatedAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AssignmentDetails {
        private EnumLabelValueResponse type;
        private TransportOrderListingResponse.Vendor identifiers;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Vendor {
        private String code;
    }
}
