package com.dpw.ctms.move.response;

import com.dpw.ctms.move.dto.TimeDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TripListingResponse {

    private String code;

    private EnumLabelValueResponse status;

    private String transportOrderCode;

    private List<CustomerOrder> customerOrders;

    private AssignmentDetails assignmentDetails;

    private Resources resources;

    private Locations locations;

    private TimeRange expectedTimes;

    private TimeRange actualTimes;

    private Long updatedAt;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerOrder {

        private String id;

        private Long updatedAt;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AssignmentDetails {

        private EnumLabelValueResponse type;

        private Vendor identifiers;

        private Long updatedAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Vendor {

        private String code;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Resources {

        private List<Vehicle> vehicles;

        private List<Trailer> trailers;

        private List<VehicleOperator> vehicleOperators;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Vehicle {

        private String id;

        private String typeCode;

        private String registrationNumber;

        private Long updatedAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Trailer {

        private String id;

        private Long updatedAt;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VehicleOperator {

        private String id;

        private EnumLabelValueResponse status;

        private Long updatedAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Locations {

        private LocationDetails origin;

        private LocationDetails destination;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationDetails {

        private String code;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange {

        private TimeDTO startAt;

        private TimeDTO endAt;
    }
}
