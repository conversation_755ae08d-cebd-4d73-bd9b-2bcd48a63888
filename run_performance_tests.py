import asyncio
import aiohttp
import time
import json
import numpy as np
from tabulate import tabulate
import copy

# --- CONFIGURATION ---
# SET THIS TO THE URL OF THE BRANCH YOU ARE TESTING
BASE_URL = "http://localhost:8093"

# SET THIS TO A UNIQUE FILENAME FOR EACH BRANCH'S RESULTS
OUTPUT_FILE = "unoptimized_results.json"

# Number of concurrent requests for the main load test
REQUESTS_PER_TEST = 1
PAGE_SIZES = [20, 50, 100, 500]

# Define the mandatory headers required by the API
REQUEST_HEADERS = {
    'Content-Type': 'application/json',
    'X-Tenant-ID': 'CFR'
}

# --- TEST SCENARIOS ---
# Define base scenarios. The script will automatically create a test for each page size.
TEST_SCENARIOS = [
    {"name": "TO List - No Filters", "endpoint": "/move/v1/transport-orders/list", "payload": {}},
    {"name": "TO List - By Trip Status (JOIN)", "endpoint": "/move/v1/transport-orders/list", "payload": {"filter": {"tripStatuses": ["CREATED", "IN_PROGRESS"]}}},
    {"name": "TO List - By Vendor ID (JOIN)", "endpoint": "/move/v1/transport-orders/list", "payload": {"filter": {"vendorIds": ["132", "135"]}}},

    {"name": "Trip List - No Filters", "endpoint": "/move/v1/trips/list", "payload": {}},
    {"name": "Trip List - By Shipment Status (JOIN)", "endpoint": "/move/v1/trips/list", "payload": {"filter": {"shipmentStatuses": ["ASSIGNED", "IN_TRANSIT"]}}},
    {"name": "Trip List - By Vehicle ID (JOIN)", "endpoint": "/move/v1/trips/list", "payload": {"filter": {"vehicleIds": ["VEH-123", "VEH-456"]}}},

    {"name": "Shipment List - No Filters", "endpoint": "/move/v1/shipments/list", "payload": {}},
    {"name": "Shipment List - By Trip Status (JOIN)", "endpoint": "/move/v1/shipments/list", "payload": {"filter": {"tripStatuses": ["COMPLETED", "CLOSED"]}}},
    {"name": "Shipment List - By Vehicle Type (JOIN)", "endpoint": "/move/v1/shipments/list", "payload": {"filter": {"vehicleTypes": ["20ft-Truck", "40ft-Truck"]}}}
]

async def fetch(session, url, payload, headers, print_errors=False):
    start_time = time.time()
    try:
        async with session.post(url, json=payload, headers=headers, timeout=60) as response:
            response_text = await response.text()
            if response.status != 200 and print_errors:
                print(f"    [Request Failed] Status: {response.status}\n    Response: {response_text[:500]}...")
            return response.status, time.time() - start_time
    except Exception as e:
        if print_errors:
            print(f"    [Connection Error] {e}")
        return None, time.time() - start_time

async def run_smoke_test(session, base_url, headers):
    print("--- Running Initial Smoke Test ---")
    if not TEST_SCENARIOS:
        print("No test scenarios defined. Skipping smoke test.")
        return True
        
    first_scenario = TEST_SCENARIOS[0]
    test_name = f"{first_scenario['name']} - Size: {PAGE_SIZES[0]}"
    print(f"Sending a single request for: '{test_name}'...")
    
    payload = copy.deepcopy(first_scenario['payload'])
    payload['pagination'] = {'pageNo': 0, 'pageSize': PAGE_SIZES[0]}
    payload['sort'] = {'sortOrder': 'asc', 'sortBy': 'createdAt'}
    url = base_url + first_scenario['endpoint']

    status, duration = await fetch(session, url, payload, headers, print_errors=True)

    if status and 200 <= status < 300:
        print(f"Smoke test PASSED. Status: {status}. Response time: {duration:.4f}s")
        print("--- Smoke Test Complete ---")
        return True
    else:
        print("! SMOKE TEST FAILED ! The server is not responding correctly.")
        print("Please check the server logs and the error message above.")
        print("Common issues: incorrect BASE_URL, wrong API endpoint path, or invalid payload structure.")
        print("--- Aborting Test Run ---")
        return False

async def run_full_test_suite(base_url, num_requests, headers):
    results = {}
    async with aiohttp.ClientSession() as session:
        if not await run_smoke_test(session, base_url, headers):
            return None

        print("--- Starting Full Load Test ---")
        for scenario in TEST_SCENARIOS:
            for size in PAGE_SIZES:
                test_name = f"{scenario['name']} - Size: {size}"
                print(f"Running test: {test_name}...")
                
                payload = copy.deepcopy(scenario['payload'])
                payload['pagination'] = {'pageNo': 0, 'pageSize': size}
                payload['sort'] = {'sortOrder': 'asc', 'sortBy': 'createdAt'}
                full_url = base_url + scenario['endpoint']
                
                tasks = [fetch(session, full_url, payload, headers) for _ in range(num_requests)]
                results[test_name] = await asyncio.gather(*tasks)
    return results

def analyze_and_format_results(all_raw_results):
    analyzed_results = {}
    display_table = []
    for test_name, raw_results in all_raw_results.items():
        response_times = [duration for status, duration in raw_results if status and 200 <= status < 300]
        total, successful = len(raw_results), len(response_times)
        metrics = {
            "total": total, "successful": successful, "failed": total - successful,
            "avg": np.mean(response_times) if successful else 0.0,
            "p95": np.percentile(response_times, 95) if successful else 0.0,
            "rps": successful / np.sum(response_times) if successful else 0.0
        }
        analyzed_results[test_name] = metrics
        display_table.append([
            test_name, f"{metrics['avg']:.4f}s", f"{metrics['p95']:.4f}s",
            f"{metrics['rps']:.2f}", f"{metrics['successful']}/{metrics['total']}"
        ])
    return analyzed_results, display_table

async def main():
    print(f"Starting performance test for: {BASE_URL}")
    print(f"Results will be saved to: {OUTPUT_FILE}\n")

    raw_results = await run_full_test_suite(BASE_URL, REQUESTS_PER_TEST, REQUEST_HEADERS)

    if not raw_results:
        print("Test run aborted due to smoke test failure.")
        return

    final_metrics, display_table = analyze_and_format_results(raw_results)

    print("\n--- Test Run Summary ---")
    headers = ["Test Case", "Avg Time", "95th Percentile", "RPS", "Success/Total"]
    print(tabulate(display_table, headers=headers, tablefmt="grid"))

    with open(OUTPUT_FILE, 'w') as f:
        json.dump(final_metrics, f, indent=4)
    
    print(f"\nResults successfully saved to {OUTPUT_FILE}")

if __name__ == "__main__":
    asyncio.run(main())